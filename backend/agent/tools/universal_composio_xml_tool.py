"""
Universal Composio XML Tool

This module provides a single, universal XML tool class that works for any Composio service.
It dynamically handles parameter parsing and execution for all Composio integrations.
"""

import logging
import json
from typing import Dict, Any, List, Optional
from agentpress.tool import Tool

logger = logging.getLogger(__name__)


class UniversalComposioXMLTool(Tool):
    """
    Universal XML tool that works for any Composio service.
    
    This single class replaces the need for service-specific tool classes.
    It dynamically handles:
    - Parameter parsing based on the generated XML schema
    - Action validation against available actions
    - Execution via ComposioXMLService
    - Result formatting
    """
    
    def __init__(
        self,
        service_name: str,
        user_id: str,
        available_actions: List[str],
        composio_service,
        connection_id: Optional[str] = None,
        xml_schema: Optional[str] = None,
        parameter_mappings: Optional[List[Dict[str, Any]]] = None,
    ):
        """
        Initialize the universal Composio XML tool.
        
        Args:
            service_name: Name of the service (e.g., "gmail", "notion")
            user_id: User ID for entity isolation
            available_actions: List of available action names for this service
            composio_service: Instance of ComposioXMLService
            connection_id: Composio connection ID for this service
            xml_schema: Generated XML schema string
            parameter_mappings: Parameter mappings for XML parsing
        """
        self.service_name = service_name
        self.user_id = user_id
        self.available_actions = available_actions
        self.composio_service = composio_service
        self.connection_id = connection_id
        self.xml_schema_content = xml_schema
        self.parameter_mappings = parameter_mappings or []
        
        # Generate tool name and XML tag
        self.tool_name = f"{service_name}_tool"
        self.xml_tag = f"{service_name}-action"
        
        super().__init__()
    
    @property
    def name(self) -> str:
        """Return the tool name."""
        return self.tool_name
    
    @property
    def xml_schema(self) -> str:
        """Return the XML schema for this tool."""
        if self.xml_schema_content:
            return self.xml_schema_content
        
        # Fallback schema if none provided
        return f"""
<{self.xml_tag} action="ACTION_NAME" [parameters]>
  [optional_content]
</{self.xml_tag}>

{self.service_name.title()} Actions:
{chr(10).join(f"- {action}" for action in self.available_actions[:10])}
{f"... and {len(self.available_actions) - 10} more actions" if len(self.available_actions) > 10 else ""}

Example:
<{self.xml_tag} action="{self.available_actions[0] if self.available_actions else 'ACTION_NAME'}">
  Optional content
</{self.xml_tag}>
"""
    
    async def execute(self, **kwargs) -> str:
        """
        Execute the XML tool by parsing parameters and calling Composio.
        
        This method:
        1. Extracts the 'action' parameter to determine which Composio action to call
        2. Parses other parameters from XML attributes and content
        3. Calls the ComposioXMLService to execute the action
        4. Returns formatted results
        
        Args:
            **kwargs: XML tool parameters including 'action' and other parameters
            
        Returns:
            Formatted execution result as string
        """
        logger.info(
            f"🎯 Universal {self.service_name} XML tool execute called for user {self.user_id}"
        )
        logger.debug(f"📝 Received kwargs: {kwargs}")
        
        try:
            # Extract action parameter
            action = kwargs.get("action")
            logger.debug(f"🔍 Extracted action: {action}")
            
            if not action:
                error_msg = "Missing required 'action' parameter"
                logger.error(f"❌ {error_msg}")
                return self._format_error(error_msg)
            
            # Validate action is available
            logger.debug(
                f"🔍 Validating action '{action}' against available actions: {self.available_actions}"
            )
            if action not in self.available_actions:
                available_str = ", ".join(self.available_actions[:5])
                if len(self.available_actions) > 5:
                    available_str += f" (and {len(self.available_actions) - 5} more)"
                error_msg = (
                    f"Action '{action}' not available for {self.service_name}. "
                    f"Available actions: {available_str}"
                )
                logger.error(f"❌ {error_msg}")
                return self._format_error(error_msg)
            
            logger.debug(f"✅ Action '{action}' is valid")
            
            # Parse parameters using dynamic mapping
            parameters = self._parse_parameters(kwargs)
            logger.debug(f"📋 Parsed parameters: {parameters}")
            
            logger.info(
                f"🚀 Executing {self.service_name} action '{action}' for user {self.user_id}"
            )
            
            # Execute via ComposioXMLService
            logger.debug(f"🔧 Calling ComposioXMLService.execute_xml_tool")
            result = await self.composio_service.execute_xml_tool(
                user_id=self.user_id,
                service_name=self.service_name,
                action=action,
                parameters=parameters,
            )
            
            logger.debug(f"📤 Got result from ComposioXMLService: {result}")
            
            # Format and return result
            if result.get("success"):
                logger.info(
                    f"🎉 Successfully executed {self.service_name} action '{action}'"
                )
                return self._format_success(action, result.get("data", {}))
            else:
                error_msg = result.get("error", "Unknown error")
                logger.error(f"❌ Execution failed: {error_msg}")
                return self._format_error(error_msg)
                
        except Exception as e:
            logger.error(
                f"💥 Error executing {self.service_name} tool: {e}", exc_info=True
            )
            return self._format_error(f"Execution error: {str(e)}")
    
    def _parse_parameters(self, kwargs: Dict[str, Any]) -> Dict[str, Any]:
        """
        Parse parameters from kwargs using dynamic parameter mappings.
        
        Args:
            kwargs: Raw parameters from XML parsing
            
        Returns:
            Parsed parameters dictionary
        """
        parameters = {}
        
        for k, v in kwargs.items():
            if k == "action":
                continue  # Skip action parameter
            elif k == "content" and v:
                # Handle content parameter - map to service-specific parameter names
                content_param = self._get_content_parameter_name()
                parameters[content_param] = v
            else:
                # Regular parameter
                parameters[k] = v
        
        return parameters
    
    def _get_content_parameter_name(self) -> str:
        """
        Get the appropriate parameter name for content based on the service.
        
        Returns:
            Parameter name for content
        """
        # Service-specific content parameter mapping
        content_mapping = {
            "gmail": "body",
            "notion": "content", 
            "github": "body",
            "slack": "text",
            "discord": "content",
            "twitter": "text",
        }
        
        return content_mapping.get(self.service_name, "content")
    
    def _format_success(self, action: str, data: Any) -> str:
        """
        Format successful execution result.
        
        Args:
            action: The action that was executed
            data: The result data from Composio
            
        Returns:
            Formatted success message
        """
        try:
            # Try to format data as JSON if it's a dict/list
            if isinstance(data, (dict, list)):
                data_str = json.dumps(data, indent=2)
            else:
                data_str = str(data)
            
            return f"✅ {self.service_name.title()} action '{action}' completed successfully.\n\nResult:\n{data_str}"
            
        except Exception as e:
            logger.warning(f"Error formatting success result: {e}")
            return f"✅ {self.service_name.title()} action '{action}' completed successfully."
    
    def _format_error(self, error_message: str) -> str:
        """
        Format error message.
        
        Args:
            error_message: The error message
            
        Returns:
            Formatted error message
        """
        return f"❌ {self.service_name.title()} tool error: {error_message}"
