"""
Composio Dynamic Schema Generator

This module provides dynamic XML schema generation from Composio's actual tool definitions.
It reads Composio's OpenAPI schemas and generates appropriate XML schemas for the agent.
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from composio_openai import ComposioToolSet, Action, App
import json

logger = logging.getLogger(__name__)


class ComposioSchemaGenerator:
    """
    Generates XML schemas dynamically from Composio's actual tool definitions.
    
    This approach:
    - Reads actual Composio tool schemas via get_action_schemas()
    - Generates XML schemas that match the real tool parameters
    - Caches schemas for performance
    - Works for any Composio service without manual coding
    """
    
    def __init__(self, api_key: str):
        """
        Initialize the schema generator.
        
        Args:
            api_key: Composio API key
        """
        self.api_key = api_key
        self.base_toolset = ComposioToolSet(api_key=api_key)
        self._schema_cache = {}
        
    def generate_xml_schema_for_service(
        self, 
        service_name: str, 
        app_enum: App, 
        available_actions: List[str]
    ) -> Tuple[str, List[Dict[str, Any]]]:
        """
        Generate XML schema for a service based on its available actions.
        
        Args:
            service_name: Name of the service (e.g., "gmail", "notion")
            app_enum: Composio App enum
            available_actions: List of available action names
            
        Returns:
            Tuple of (xml_schema_string, parameter_mappings)
        """
        cache_key = f"{service_name}:{':'.join(sorted(available_actions))}"
        
        if cache_key in self._schema_cache:
            logger.debug(f"📋 Using cached schema for {service_name}")
            return self._schema_cache[cache_key]
            
        logger.info(f"🔧 Generating XML schema for {service_name} with {len(available_actions)} actions")
        
        try:
            # Get action schemas from Composio
            action_enums = []
            for action_name in available_actions:
                try:
                    action_enum = Action[action_name]
                    action_enums.append(action_enum)
                except KeyError:
                    logger.warning(f"⚠️ Action {action_name} not found in Composio Action enum")
                    continue
            
            if not action_enums:
                logger.warning(f"⚠️ No valid actions found for {service_name}")
                return self._generate_fallback_schema(service_name, available_actions)
            
            # Get schemas for these actions
            logger.debug(f"🔍 Fetching schemas for {len(action_enums)} actions")
            action_schemas = self.base_toolset.get_action_schemas(
                actions=action_enums,
                check_connected_accounts=False  # Allow schema inspection without connections
            )
            
            # Generate XML schema from the action schemas
            xml_schema, mappings = self._generate_xml_from_schemas(
                service_name, action_schemas, available_actions
            )
            
            # Cache the result
            result = (xml_schema, mappings)
            self._schema_cache[cache_key] = result
            
            logger.info(f"✅ Generated XML schema for {service_name}")
            return result
            
        except Exception as e:
            logger.error(f"💥 Error generating schema for {service_name}: {e}")
            return self._generate_fallback_schema(service_name, available_actions)
    
    def _generate_xml_from_schemas(
        self, 
        service_name: str, 
        action_schemas: List[Any], 
        available_actions: List[str]
    ) -> Tuple[str, List[Dict[str, Any]]]:
        """
        Generate XML schema from Composio action schemas.
        
        Args:
            service_name: Service name
            action_schemas: List of Composio ActionModel objects
            available_actions: List of available action names
            
        Returns:
            Tuple of (xml_schema_string, parameter_mappings)
        """
        xml_tag = f"{service_name}-action"
        
        # Build parameter mappings for XML parsing
        mappings = [
            {"param_name": "action", "node_type": "attribute", "path": ".", "required": True}
        ]
        
        # Analyze common parameters across actions
        common_params = self._analyze_common_parameters(action_schemas)
        
        # Add common parameters as attributes
        for param_name, param_info in common_params.items():
            if param_name != "action":  # Skip action as it's already added
                mappings.append({
                    "param_name": param_name,
                    "node_type": "attribute",
                    "path": ".",
                    "required": param_info.get("required", False)
                })
        
        # Add content mapping for body/message parameters
        content_params = ["body", "content", "message", "text", "description"]
        mappings.append({
            "param_name": "content",
            "node_type": "content",
            "path": ".",
            "required": False
        })
        
        # Generate XML schema documentation
        schema_parts = [
            f"<{xml_tag} action=\"ACTION_NAME\" [parameters]>",
            "  [optional_content]",
            f"</{xml_tag}>",
            "",
            f"{service_name.title()} Actions:"
        ]
        
        # Add action documentation
        for i, action_name in enumerate(available_actions[:10]):  # Limit to 10 for readability
            # Try to get description from schema
            description = "No description available"
            if i < len(action_schemas) and hasattr(action_schemas[i], 'description'):
                description = action_schemas[i].description or description
            
            schema_parts.append(f"- {action_name}: {description}")
        
        if len(available_actions) > 10:
            schema_parts.append(f"... and {len(available_actions) - 10} more actions")
        
        # Add example
        schema_parts.extend([
            "",
            "Example:",
            f"<{xml_tag} action=\"{available_actions[0] if available_actions else 'ACTION_NAME'}\" param1=\"value1\">",
            "  Optional content for body/message parameters",
            f"</{xml_tag}>"
        ])
        
        xml_schema = "\n".join(schema_parts)
        
        return xml_schema, mappings
    
    def _analyze_common_parameters(self, action_schemas: List[Any]) -> Dict[str, Dict[str, Any]]:
        """
        Analyze action schemas to find common parameters.
        
        Args:
            action_schemas: List of Composio ActionModel objects
            
        Returns:
            Dictionary of common parameters and their info
        """
        param_counts = {}
        param_info = {}
        
        for schema in action_schemas:
            if hasattr(schema, 'parameters') and hasattr(schema.parameters, 'properties'):
                properties = schema.parameters.properties
                if isinstance(properties, dict):
                    for param_name, param_def in properties.items():
                        param_counts[param_name] = param_counts.get(param_name, 0) + 1
                        if param_name not in param_info:
                            param_info[param_name] = {
                                "type": getattr(param_def, 'type', 'string'),
                                "description": getattr(param_def, 'description', ''),
                                "required": False
                            }
        
        # Return parameters that appear in multiple actions (common ones)
        total_actions = len(action_schemas)
        common_threshold = max(1, total_actions // 3)  # Appear in at least 1/3 of actions
        
        common_params = {
            param: info for param, info in param_info.items()
            if param_counts[param] >= common_threshold
        }
        
        return common_params
    
    def _generate_fallback_schema(
        self, 
        service_name: str, 
        available_actions: List[str]
    ) -> Tuple[str, List[Dict[str, Any]]]:
        """
        Generate a fallback XML schema when dynamic generation fails.
        
        Args:
            service_name: Service name
            available_actions: List of available action names
            
        Returns:
            Tuple of (xml_schema_string, parameter_mappings)
        """
        logger.info(f"🔄 Generating fallback schema for {service_name}")
        
        xml_tag = f"{service_name}-action"
        
        # Basic parameter mappings
        mappings = [
            {"param_name": "action", "node_type": "attribute", "path": ".", "required": True},
            {"param_name": "content", "node_type": "content", "path": ".", "required": False}
        ]
        
        # Simple XML schema
        schema_parts = [
            f"<{xml_tag} action=\"ACTION_NAME\" [parameters]>",
            "  [optional_content]",
            f"</{xml_tag}>",
            "",
            f"{service_name.title()} Actions:",
        ]
        
        for action_name in available_actions[:10]:
            schema_parts.append(f"- {action_name}")
        
        if len(available_actions) > 10:
            schema_parts.append(f"... and {len(available_actions) - 10} more actions")
        
        xml_schema = "\n".join(schema_parts)
        
        return xml_schema, mappings
