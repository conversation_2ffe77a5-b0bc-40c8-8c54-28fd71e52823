"""
Composio XML Tool - Base class for XML-wrapped Composio tools

This module provides the base XML tool class that wraps Composio's OpenAI integration
with XML schemas, enabling seamless integration with the Atlas agent's XML tool system.
"""

import logging
import json
import xml.etree.ElementTree as ET
from typing import Dict, Any, List, Optional
from abc import ABC, abstractmethod

# Import the base Tool class from agentpress
from agentpress.tool import Tool

logger = logging.getLogger(__name__)


class ComposioXMLTool(Tool, ABC):
    """
    Base class for XML tools that wrap Composio services.

    This class provides the foundation for creating XML-based tools that execute
    Composio actions with proper user isolation and error handling.

    Key Features:
    - XML schema generation based on available Composio actions
    - Parameter parsing from XML attributes and content
    - User-specific execution with entity isolation
    - Graceful error handling and validation
    """

    def __init__(
        self,
        service_name: str,
        user_id: str,
        available_actions: List[str],
        composio_service,
        connection_id: Optional[str] = None,
    ):
        """
        Initialize the Composio XML tool.

        Args:
            service_name: Name of the service (e.g., "gmail", "notion")
            user_id: User ID for entity isolation
            available_actions: List of available action names for this service
            composio_service: Instance of ComposioXMLService
            connection_id: Composio connection ID for this service
        """
        self.service_name = service_name
        self.user_id = user_id
        self.available_actions = available_actions
        self.composio_service = composio_service
        self.connection_id = connection_id

        # Generate tool name and XML tag
        self.tool_name = f"{service_name}_tool"
        self.xml_tag = f"{service_name}-action"

        super().__init__()

    @property
    def name(self) -> str:
        """Return the tool name."""
        return self.tool_name

    @property
    @abstractmethod
    def xml_schema(self) -> str:
        """
        Return the XML schema for this tool.

        This should be implemented by each service-specific tool class
        to provide the appropriate XML schema based on available actions.
        """
        pass

    async def execute(self, **kwargs) -> str:
        """
        Execute the XML tool by parsing parameters and calling Composio.

        This method:
        1. Extracts the 'action' parameter to determine which Composio action to call
        2. Parses other parameters from XML attributes and content
        3. Calls the ComposioXMLService to execute the action
        4. Returns formatted results

        Args:
            **kwargs: XML tool parameters including 'action' and other parameters

        Returns:
            Formatted execution result as string
        """
        logger.info(
            f"🎯 {self.service_name} XML tool execute called for user {self.user_id}"
        )
        logger.debug(f"📝 Received kwargs: {kwargs}")

        try:
            # Extract action parameter
            action = kwargs.get("action")
            logger.debug(f"🔍 Extracted action: {action}")

            if not action:
                error_msg = "Missing required 'action' parameter"
                logger.error(f"❌ {error_msg}")
                return self._format_error(error_msg)

            # Validate action is available
            logger.debug(
                f"🔍 Validating action '{action}' against available actions: {self.available_actions}"
            )
            if action not in self.available_actions:
                available_str = ", ".join(self.available_actions[:5])
                if len(self.available_actions) > 5:
                    available_str += f" (and {len(self.available_actions) - 5} more)"
                error_msg = (
                    f"Action '{action}' not available for {self.service_name}. "
                    f"Available actions: {available_str}"
                )
                logger.error(f"❌ {error_msg}")
                return self._format_error(error_msg)

            logger.debug(f"✅ Action '{action}' is valid")

            # Extract parameters - handle both parsed parameters and content
            parameters = {}
            for k, v in kwargs.items():
                if k == "action":
                    continue  # Skip action parameter
                elif k == "content" and v:
                    # If there's content, it might be the body/message content
                    # Map it to common parameter names based on the service
                    if self.service_name == "gmail":
                        parameters["body"] = v
                    elif self.service_name == "notion":
                        parameters["content"] = v
                    elif self.service_name == "github":
                        parameters["body"] = v
                    else:
                        parameters["content"] = v
                else:
                    # Regular parameter
                    parameters[k] = v

            logger.debug(f"📋 Extracted parameters: {parameters}")

            logger.info(
                f"🚀 Executing {self.service_name} action '{action}' for user {self.user_id}"
            )

            # Execute via ComposioXMLService
            logger.debug(f"🔧 Calling ComposioXMLService.execute_xml_tool")
            result = await self.composio_service.execute_xml_tool(
                user_id=self.user_id,
                service_name=self.service_name,
                action=action,
                parameters=parameters,
            )

            logger.debug(f"📤 Got result from ComposioXMLService: {result}")

            # Format and return result
            if result.get("success"):
                logger.info(
                    f"🎉 Successfully executed {self.service_name} action '{action}'"
                )
                return self._format_success(action, result.get("data", {}))
            else:
                error_msg = result.get("error", "Unknown error")
                logger.error(f"❌ Execution failed: {error_msg}")
                return self._format_error(error_msg)

        except Exception as e:
            logger.error(
                f"💥 Error executing {self.service_name} tool: {e}", exc_info=True
            )
            return self._format_error(f"Execution error: {str(e)}")

    def _format_success(self, action: str, data: Any) -> str:
        """
        Format successful execution result.

        Args:
            action: The action that was executed
            data: The result data from Composio

        Returns:
            Formatted success message
        """
        try:
            # Try to format data as JSON if it's a dict/list
            if isinstance(data, (dict, list)):
                data_str = json.dumps(data, indent=2)
            else:
                data_str = str(data)

            return f"✅ {self.service_name.title()} action '{action}' completed successfully.\n\nResult:\n{data_str}"

        except Exception as e:
            logger.warning(f"Error formatting success result: {e}")
            return f"✅ {self.service_name.title()} action '{action}' completed successfully."

    def _format_error(self, error_message: str) -> str:
        """
        Format error message.

        Args:
            error_message: The error message

        Returns:
            Formatted error message
        """
        return f"❌ {self.service_name.title()} tool error: {error_message}"

    def _generate_base_xml_schema(self, actions_info: List[Dict[str, str]]) -> str:
        """
        Generate base XML schema for the service.

        Args:
            actions_info: List of action information dictionaries

        Returns:
            XML schema string
        """
        schema_parts = [
            f'<{self.xml_tag} action="ACTION_NAME" [additional_parameters]>',
            "  [optional_content]",
            f"</{self.xml_tag}>",
            "",
            f"Available actions for {self.service_name}:",
        ]

        for action_info in actions_info[:10]:  # Limit to first 10 for readability
            action_name = action_info.get("name", "unknown")
            description = action_info.get("description", "No description available")
            schema_parts.append(f"- {action_name}: {description}")

        if len(actions_info) > 10:
            schema_parts.append(f"... and {len(actions_info) - 10} more actions")

        return "\n".join(schema_parts)


class GmailXMLTool(ComposioXMLTool):
    """Gmail-specific XML tool implementation."""

    @property
    def xml_schema(self) -> str:
        """Return Gmail-specific XML schema."""
        return f"""
<{self.xml_tag} action="ACTION_NAME" [parameters]>
  [optional_content]
</{self.xml_tag}>

Gmail Actions:
- send_email: Send an email (requires: to, subject, body)
- read_email: Read emails (optional: query, max_results)
- search_emails: Search emails (requires: query)
- create_draft: Create email draft (requires: to, subject, body)
- reply_to_email: Reply to an email (requires: message_id, body)

Example:
<{self.xml_tag} action="send_email" to="<EMAIL>" subject="Hello">
  This is the email body content.
</{self.xml_tag}>
"""


class NotionXMLTool(ComposioXMLTool):
    """Notion-specific XML tool implementation."""

    @property
    def xml_schema(self) -> str:
        """Return Notion-specific XML schema."""
        return f"""
<{self.xml_tag} action="ACTION_NAME" [parameters]>
  [optional_content]
</{self.xml_tag}>

Notion Actions:
- create_page: Create a new page (requires: title, content)
- update_page: Update existing page (requires: page_id, content)
- search_pages: Search pages (requires: query)
- get_page: Get page details (requires: page_id)
- create_database: Create database (requires: title, properties)

Example:
<{self.xml_tag} action="create_page" title="My New Page">
  This is the page content in markdown format.
</{self.xml_tag}>
"""


class GitHubXMLTool(ComposioXMLTool):
    """GitHub-specific XML tool implementation."""

    @property
    def xml_schema(self) -> str:
        """Return GitHub-specific XML schema."""
        return f"""
<{self.xml_tag} action="ACTION_NAME" [parameters]>
  [optional_content]
</{self.xml_tag}>

GitHub Actions:
- create_issue: Create an issue (requires: owner, repo, title, body)
- create_pull_request: Create PR (requires: owner, repo, title, head, base, body)
- get_repository: Get repo details (requires: owner, repo)
- list_issues: List issues (requires: owner, repo)
- star_repository: Star a repository (requires: owner, repo)

Example:
<{self.xml_tag} action="create_issue" owner="user" repo="project" title="Bug Report">
  Detailed description of the bug and steps to reproduce.
</{self.xml_tag}>
"""
