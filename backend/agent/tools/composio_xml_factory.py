"""
Composio XML Tool Factory

This module provides a factory for creating XML tool instances based on user's
active Composio connections. It dynamically generates the appropriate XML tools
for each connected service.
"""

import logging
from typing import List, Dict, Any, Optional, Type
from .composio_xml_tool import (
    ComposioXMLTool,
    GmailXMLTool,
    NotionXMLTool,
    GitHubXMLTool,
)
from services.composio_openai_service import ComposioXMLService

logger = logging.getLogger(__name__)


class ComposioXMLToolFactory:
    """
    Factory for creating XML tool instances based on user's Composio connections.

    This factory:
    - Discovers user's active Composio connections
    - Creates appropriate XML tool instances for each service
    - Handles service-specific tool class mapping
    - Provides graceful error handling for unsupported services
    """

    def __init__(self, composio_service: Optional[ComposioXMLService] = None):
        """
        Initialize the tool factory.

        Args:
            composio_service: Optional ComposioXMLService instance.
                            If None, creates one from environment.
        """
        self.composio_service = composio_service or ComposioXMLService.from_env()

        # Mapping of service names to their XML tool classes
        self.service_tool_mapping = {
            "gmail": GmailXMLTool,
            "notion": NotionXMLTool,
            "github": GitHubXMLTool,
            # Add more service mappings as we implement them
        }

    async def create_user_tools(self, user_id: str) -> List[ComposioXMLTool]:
        """
        Create XML tool instances for a user's active Composio connections.

        This method:
        1. Gets user's available tools from ComposioXMLService
        2. Creates appropriate XML tool instances for each service
        3. Handles errors gracefully, continuing with other services if one fails

        Args:
            user_id: User ID to create tools for

        Returns:
            List of XML tool instances for the user's connected services
        """
        logger.info(f"🏭 create_user_tools called for user: {user_id}")

        try:
            logger.debug(
                f"🔍 Getting available tools from ComposioXMLService for user {user_id}"
            )

            # Get user's available tools from Composio
            available_tools = await self.composio_service.get_user_available_tools(
                user_id
            )

            logger.debug(
                f"📊 Found {len(available_tools)} available tool services for user {user_id}"
            )

            if not available_tools:
                logger.info(f"ℹ️ No available tools found for user {user_id}")
                return []

            xml_tools = []

            for i, tool_info in enumerate(available_tools):
                logger.debug(
                    f"🔧 Processing tool {i+1}/{len(available_tools)}: {tool_info}"
                )

                try:
                    service_name = tool_info["service_name"]
                    available_actions = tool_info["available_actions"]
                    connection_id = tool_info["connection_id"]

                    logger.debug(
                        f"🎯 Creating XML tool for {service_name} with {len(available_actions)} actions"
                    )

                    # Create XML tool for this service
                    xml_tool = self._create_service_tool(
                        service_name=service_name,
                        user_id=user_id,
                        available_actions=available_actions,
                        connection_id=connection_id,
                    )

                    if xml_tool:
                        xml_tools.append(xml_tool)
                        logger.info(
                            f"✅ Created {service_name} XML tool for user {user_id} "
                            f"with {len(available_actions)} actions"
                        )
                    else:
                        logger.warning(
                            f"⚠️ Could not create XML tool for {service_name} "
                            f"(user: {user_id}) - service not supported"
                        )

                except Exception as service_error:
                    service_name = tool_info.get("service_name", "unknown")
                    logger.error(
                        f"💥 Error creating tool for service {service_name}: {service_error}",
                        exc_info=True,
                    )
                    # Continue with other services
                    continue

            logger.info(
                f"🎉 Successfully created {len(xml_tools)} XML tools for user {user_id}"
            )

            if xml_tools:
                for tool in xml_tools:
                    logger.debug(
                        f"  📋 {tool.service_name}: {tool.xml_tag} with {len(tool.available_actions)} actions"
                    )

            return xml_tools

        except Exception as e:
            logger.error(
                f"💥 Error creating XML tools for user {user_id}: {e}", exc_info=True
            )
            return []

    def _create_service_tool(
        self,
        service_name: str,
        user_id: str,
        available_actions: List[str],
        connection_id: Optional[str] = None,
    ) -> Optional[ComposioXMLTool]:
        """
        Create an XML tool instance for a specific service.

        Args:
            service_name: Name of the service (e.g., "gmail", "notion")
            user_id: User ID
            available_actions: List of available action names
            connection_id: Composio connection ID

        Returns:
            XML tool instance or None if service not supported
        """
        try:
            # Get the appropriate tool class for this service
            tool_class = self.service_tool_mapping.get(service_name.lower())

            if not tool_class:
                logger.warning(
                    f"No XML tool class found for service: {service_name}. "
                    f"Supported services: {list(self.service_tool_mapping.keys())}"
                )
                return None

            # Create and return the tool instance
            return tool_class(
                service_name=service_name,
                user_id=user_id,
                available_actions=available_actions,
                composio_service=self.composio_service,
                connection_id=connection_id,
            )

        except Exception as e:
            logger.error(f"Error creating {service_name} tool: {e}")
            return None

    def get_supported_services(self) -> List[str]:
        """
        Get list of supported service names.

        Returns:
            List of supported service names
        """
        return list(self.service_tool_mapping.keys())

    def is_service_supported(self, service_name: str) -> bool:
        """
        Check if a service is supported by the factory.

        Args:
            service_name: Name of the service to check

        Returns:
            True if service is supported, False otherwise
        """
        return service_name.lower() in self.service_tool_mapping

    async def get_user_tool_info(self, user_id: str) -> Dict[str, Any]:
        """
        Get information about available tools for a user.

        This method provides metadata about what tools are available
        without actually creating the tool instances.

        Args:
            user_id: User ID

        Returns:
            Dictionary containing tool information
        """
        try:
            available_tools = await self.composio_service.get_user_available_tools(
                user_id
            )

            tool_info = {
                "user_id": user_id,
                "total_services": len(available_tools),
                "supported_services": 0,
                "unsupported_services": 0,
                "services": [],
            }

            for tool_data in available_tools:
                service_name = tool_data["service_name"]
                is_supported = self.is_service_supported(service_name)

                service_info = {
                    "service_name": service_name,
                    "supported": is_supported,
                    "action_count": len(tool_data["available_actions"]),
                    "connection_id": tool_data["connection_id"],
                }

                tool_info["services"].append(service_info)

                if is_supported:
                    tool_info["supported_services"] += 1
                else:
                    tool_info["unsupported_services"] += 1

            return tool_info

        except Exception as e:
            logger.error(f"Error getting tool info for user {user_id}: {e}")
            return {
                "user_id": user_id,
                "error": str(e),
                "total_services": 0,
                "supported_services": 0,
                "unsupported_services": 0,
                "services": [],
            }

    def add_service_mapping(self, service_name: str, tool_class: Type[ComposioXMLTool]):
        """
        Add a new service to tool class mapping.

        This allows for dynamic extension of supported services.

        Args:
            service_name: Name of the service
            tool_class: XML tool class for the service
        """
        self.service_tool_mapping[service_name.lower()] = tool_class
        logger.info(f"Added service mapping: {service_name} -> {tool_class.__name__}")

    async def validate_user_connections(self, user_id: str) -> Dict[str, bool]:
        """
        Validate that user's connections are still active.

        Args:
            user_id: User ID

        Returns:
            Dictionary mapping service names to their connection status
        """
        try:
            active_services = await self.composio_service.get_user_active_services(
                user_id
            )
            validation_results = {}

            for service_name in active_services:
                try:
                    # Check connection status via auth service
                    status = await self.composio_service.auth_service.check_connection_status(
                        user_id, service_name
                    )
                    validation_results[service_name] = status.connected

                except Exception as e:
                    logger.error(f"Error validating {service_name} connection: {e}")
                    validation_results[service_name] = False

            return validation_results

        except Exception as e:
            logger.error(f"Error validating connections for user {user_id}: {e}")
            return {}
